from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import TVShow, Season, Episode, MediaRequest
from .services import OverseerrDataSyncService, OverseerrAPIError, TVShowService
from .scheduler import get_scheduler_status
from .jackett_service import JackettService, JackettAPIError
import logging

logger = logging.getLogger(__name__)


def dashboard(request):
    """Main dashboard view showing all TV shows"""
    # Get filter and sort parameters
    sort_by = request.GET.get('sort', 'title')
    search_query = request.GET.get('search', '')

    # Get view mode from URL parameter, cookie, or default to grid
    view_mode = request.GET.get('view')
    if not view_mode:
        view_mode = request.COOKIES.get('dashboard_view_mode', 'grid')

    # Validate view mode
    if view_mode not in ['grid', 'table']:
        view_mode = 'grid'

    # Base queryset
    tv_shows = TVShow.objects.select_related('media_request').prefetch_related('seasons__episodes')

    # Apply search filter
    if search_query:
        tv_shows = tv_shows.filter(
            Q(title__icontains=search_query) |
            Q(overview__icontains=search_query)
        )



    # Apply sorting
    sort_options = {
        'title': 'title',
        '-title': '-title',
        'date_added': 'created_at',
        '-date_added': '-created_at',
        'air_date': 'first_air_date',
        '-air_date': '-first_air_date',
        'request_date': 'media_request__requested_at',
        '-request_date': '-media_request__requested_at',
    }

    if sort_by in sort_options:
        tv_shows = tv_shows.order_by(sort_options[sort_by])

    # Pagination - adjust items per page based on view mode
    items_per_page = 12 if view_mode == 'grid' else 25
    paginator = Paginator(tv_shows, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    stats = {
        'total_shows': TVShow.objects.count(),
        'shows_with_episodes': TVShow.objects.filter(seasons__episodes__quality__isnull=False).distinct().count(),
    }

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'current_filters': {
            'sort': sort_by,
            'search': search_query,
            'view': view_mode,
        },
        'sort_choices': [
            ('title', 'Title A-Z'),
            ('-title', 'Title Z-A'),
            ('date_added', 'Date Added (Oldest)'),
            ('-date_added', 'Date Added (Newest)'),
            ('air_date', 'Air Date (Oldest)'),
            ('-air_date', 'Air Date (Newest)'),
            ('request_date', 'Request Date (Oldest)'),
            ('-request_date', 'Request Date (Newest)'),
        ],
    }

    response = render(request, 'media_requests/dashboard.html', context)

    # Set view mode cookie if it was changed via URL parameter
    if request.GET.get('view') and request.GET.get('view') != request.COOKIES.get('dashboard_view_mode'):
        response.set_cookie('dashboard_view_mode', view_mode, max_age=365*24*60*60)  # 1 year

    return response


def show_detail(request, show_id):
    """Detailed view for a specific TV show"""
    tv_show = get_object_or_404(
        TVShow.objects.select_related('media_request').prefetch_related(
            'seasons__episodes__quality',
            'seasons__quality'
        ),
        id=show_id
    )

    # Get seasons with episode counts
    seasons = tv_show.seasons.annotate(
        total_episodes=Count('episodes'),
        available_episodes=Count('episodes', filter=Q(episodes__quality__isnull=False))
    ).order_by('season_number')

    context = {
        'tv_show': tv_show,
        'seasons': seasons,
    }

    return render(request, 'media_requests/show_detail.html', context)


def season_episodes_ajax(request, season_id):
    """AJAX view to get episodes for a season"""
    season = get_object_or_404(Season, id=season_id)
    episodes = season.episodes.select_related('quality', 'download_status').order_by('episode_number')

    episodes_data = []
    for episode in episodes:
        # Get download status information
        download_status_info = None
        if hasattr(episode, 'download_status') and episode.download_status:
            download_status_info = {
                'status': episode.download_status.status,
                'torrent_title': episode.download_status.torrent_title,
                'quality': episode.download_status.quality,
                'indexer': episode.download_status.indexer,
                'error_message': episode.download_status.error_message,
                'started_at': episode.download_status.started_at.isoformat() if episode.download_status.started_at else None,
                'completed_at': episode.download_status.completed_at.isoformat() if episode.download_status.completed_at else None,
            }

        episodes_data.append({
            'id': episode.id,
            'episode_number': episode.episode_number,
            'name': episode.name,
            'overview': episode.overview,
            'air_date': episode.air_date.isoformat() if episode.air_date else None,
            'runtime': episode.runtime,

            'quality': {
                'resolution': episode.quality.resolution if episode.quality else None,
                'source': episode.quality.source if episode.quality else None,
                'codec': episode.quality.codec if episode.quality else None,
            } if episode.quality else None,
            'download_status': download_status_info,
        })

    return JsonResponse({
        'episodes': episodes_data,
        'season': {
            'id': season.id,
            'season_number': season.season_number,
            'name': season.name,

        }
    })


def sync_data(request):
    """View to trigger data synchronization from Overseerr"""
    if request.method == 'POST':
        try:
            sync_service = OverseerrDataSyncService()
            sync_service.sync_all_tv_requests()

            messages.success(request, 'Successfully synchronized data from Overseerr!')
            logger.info('Manual data sync completed successfully')

        except OverseerrAPIError as e:
            messages.error(request, f'Failed to sync data from Overseerr: {e}')
            logger.error(f'Manual data sync failed: {e}')
        except Exception as e:
            messages.error(request, f'Unexpected error during sync: {e}')
            logger.error(f'Unexpected error during manual sync: {e}')

    return redirect('dashboard')


def api_status(request):
    """API endpoint to check Overseerr connection status"""
    try:
        sync_service = OverseerrDataSyncService()
        # Try to make a simple API call to test connection
        sync_service.client._make_request('/request', params={'take': 1})

        return JsonResponse({
            'status': 'connected',
            'message': 'Successfully connected to Overseerr API'
        })
    except OverseerrAPIError as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Unexpected error: {e}'
        }, status=500)


def scheduler_status(request):
    """API endpoint to check scheduler status"""
    try:
        status = get_scheduler_status()
        return JsonResponse(status)
    except Exception as e:
        return JsonResponse({
            'running': False,
            'error': str(e)
        }, status=500)


def search_shows(request):
    """Search for TV shows via Overseerr API"""
    query = request.GET.get('q', '').strip()
    page = int(request.GET.get('page', 1))

    if not query:
        return JsonResponse({
            'results': [],
            'total_results': 0,
            'total_pages': 0,
            'page': page
        })

    try:
        sync_service = OverseerrDataSyncService()
        search_results = sync_service.client.search_tv_shows(query, page)

        return JsonResponse(search_results)

    except OverseerrAPIError as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'error': f'Unexpected error: {e}'
        }, status=500)


def add_show(request):
    """Add a TV show directly to our database"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body)
        tmdb_id = data.get('tmdb_id')
        added_by = data.get('added_by', 'Web User')

        if not tmdb_id:
            return JsonResponse({'error': 'tmdb_id is required'}, status=400)

        # Check if show already exists
        if TVShow.objects.filter(tmdb_id=tmdb_id).exists():
            return JsonResponse({
                'error': 'Show already exists in database'
            }, status=400)

        # Add show to our database
        tv_service = TVShowService()
        tv_show = tv_service.add_tv_show_from_tmdb(tmdb_id, added_by)

        return JsonResponse({
            'success': True,
            'message': f'Successfully added "{tv_show.title}" to your library',
            'show_id': tv_show.id,
            'show_title': tv_show.title
        })

    except Exception as e:
        logger.error(f"Failed to add show: {e}")
        return JsonResponse({
            'error': f'Failed to add show: {str(e)}'
        }, status=500)


def delete_show(request, show_id):
    """Delete a TV show from our database"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body) if request.body else {}
        deleted_by = data.get('deleted_by', 'Web User')

        # Get the show to check if it exists and get its title
        try:
            tv_show = TVShow.objects.get(id=show_id)
            show_title = tv_show.title
        except TVShow.DoesNotExist:
            return JsonResponse({
                'error': 'Show not found'
            }, status=404)

        # Delete the show
        tv_service = TVShowService()
        success = tv_service.delete_tv_show(show_id, deleted_by)

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Successfully deleted "{show_title}"'
            })
        else:
            return JsonResponse({
                'error': 'Failed to delete show'
            }, status=500)

    except Exception as e:
        logger.error(f"Failed to delete show: {e}")
        return JsonResponse({
            'error': f'Failed to delete show: {str(e)}'
        }, status=500)


def jackett_search_episode(request):
    """Search for a specific episode using Jackett"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title', '').strip()
        season = request.GET.get('season', '')
        episode = request.GET.get('episode', '')
        indexer = request.GET.get('indexer', 'all')
        filter_results = request.GET.get('filter', 'true').lower() == 'true'

        # Validate parameters
        if not show_title:
            return JsonResponse({'error': 'show_title is required'}, status=400)

        if not season or not episode:
            return JsonResponse({'error': 'season and episode are required'}, status=400)

        try:
            season_num = int(season)
            episode_num = int(episode)
        except ValueError:
            return JsonResponse({'error': 'season and episode must be integers'}, status=400)

        # Initialize Jackett service
        jackett_service = JackettService()

        # Perform search
        results = jackett_service.search_episode(show_title, season_num, episode_num, indexer, filter_results)

        # Get quality profile for scoring (try to find the show's profile)
        quality_profile = None
        try:
            from .models import TVShow
            tv_show = TVShow.objects.filter(title__icontains=show_title).first()
            if tv_show and tv_show.quality_profile:
                quality_profile = tv_show.quality_profile
        except Exception as e:
            logger.debug(f"Could not get quality profile for scoring: {e}")

        # Calculate scores for all results
        results = jackett_service.calculate_scores_for_results(results, quality_profile)

        # Sort by score (highest first), then by seeders
        results.sort(key=lambda x: (x.score, x.seeders), reverse=True)

        # Convert results to JSON-serializable format
        results_data = [result.to_dict() for result in results]

        return JsonResponse({
            'success': True,
            'results': results_data,
            'total_results': len(results_data),
            'search_query': f"{show_title} S{season_num:02d}E{episode_num:02d}",
            'filtered': filter_results,
        })

    except JackettAPIError as e:
        logger.error(f"Jackett search failed: {e}")
        return JsonResponse({
            'error': f'Jackett search failed: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in Jackett search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_status(request):
    """Check Jackett connection status"""
    try:
        jackett_service = JackettService()

        # Get configuration info
        config_info = {
            'base_url': jackett_service.base_url,
            'api_key_configured': bool(jackett_service.api_key),
            'api_key_length': len(jackett_service.api_key) if jackett_service.api_key else 0
        }

        # Test connection
        is_connected = jackett_service.test_connection()

        if is_connected:
            # Get indexers if connection is successful
            indexers = jackett_service.get_indexers()

            return JsonResponse({
                'status': 'connected',
                'message': 'Successfully connected to Jackett API',
                'config': config_info,
                'indexers': indexers,
                'total_indexers': len(indexers),
                'configured_indexers': len([idx for idx in indexers if idx.get('configured', False)])
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Failed to connect to Jackett API',
                'config': config_info,
                'help': 'Check your JACKETT_BASE_URL and JACKETT_API_KEY in .env file'
            }, status=500)

    except JackettAPIError as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e),
            'help': 'Check your Jackett configuration and ensure Jackett is running'
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Unexpected error: {e}',
            'help': 'Check your Jackett configuration and ensure Jackett is running'
        }, status=500)


def jackett_download(request):
    """Handle download request for a torrent with Real Debrid integration"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body)

        # Get download parameters
        download_url = data.get('download_url', '')
        magnet_url = data.get('magnet_url', '')
        title = data.get('title', '')
        episode_id = data.get('episode_id')  # New parameter to link to episode

        if not magnet_url and not download_url:
            return JsonResponse({'error': 'Either magnet_url or download_url is required'}, status=400)

        if not episode_id:
            return JsonResponse({'error': 'episode_id is required'}, status=400)

        # Get the episode
        try:
            from .models import Episode, DownloadStatus, MediaQuality
            episode = Episode.objects.get(id=episode_id)
        except Episode.DoesNotExist:
            return JsonResponse({'error': 'Episode not found'}, status=404)

        # Check if there's already an active download for this episode
        existing_download = None
        try:
            existing_download = DownloadStatus.objects.get(
                episode=episode,
                status__in=['pending', 'downloading', 'monitoring', 'completed']
            )
        except DownloadStatus.DoesNotExist:
            pass
        except DownloadStatus.MultipleObjectsReturned:
            # If there are multiple, get the most recent one
            existing_download = DownloadStatus.objects.filter(
                episode=episode,
                status__in=['pending', 'downloading', 'monitoring', 'completed']
            ).order_by('-started_at').first()

        # If there's an existing download, check if the new one is better quality
        if existing_download:
            # Get the show's quality profile for proper comparison
            tv_show = episode.season.tv_show
            quality_profile = tv_show.quality_profile if hasattr(tv_show, 'quality_profile') else None

            # Create quality objects for comparison
            new_download_quality = None
            existing_download_quality = existing_download.get_quality_object()

            # Create a temporary quality object for the new download
            new_quality_str = data.get('quality', '')
            if new_quality_str:
                # Try to determine source and codec from title
                title_upper = title.upper()
                source = 'Unknown'
                codec = 'Unknown'

                if 'REMUX' in title_upper:
                    source = 'REMUX'
                elif 'BLURAY' in title_upper or 'BLU-RAY' in title_upper:
                    source = 'BluRay'
                elif 'WEB-DL' in title_upper:
                    source = 'WEB-DL'
                elif 'WEBRIP' in title_upper:
                    source = 'WEBRip'
                elif 'WEB' in title_upper:
                    source = 'WEB'
                elif 'HDTV' in title_upper:
                    source = 'HDTV'

                if 'AV1' in title_upper:
                    codec = 'AV1'
                elif 'H265' in title_upper or 'HEVC' in title_upper:
                    codec = 'h265'
                elif 'H264' in title_upper or 'AVC' in title_upper:
                    codec = 'h264'

                try:
                    new_download_quality, _ = MediaQuality.objects.get_or_create(
                        resolution=new_quality_str,
                        source=source,
                        codec=codec,
                        defaults={}
                    )
                except Exception:
                    pass

            # Determine if this is a quality upgrade using profile rules
            is_upgrade = False
            if new_download_quality and existing_download_quality:
                is_upgrade = new_download_quality.is_better_than(existing_download_quality, quality_profile)
            elif new_download_quality:
                # If we can't get existing quality, allow the download
                is_upgrade = True
            else:
                # Fallback to simple string comparison
                quality_order = ['480p', '720p', '1080p', '4K', '2160p']
                try:
                    new_idx = quality_order.index(new_quality_str)
                    existing_idx = quality_order.index(existing_download.quality)
                    is_upgrade = new_idx > existing_idx
                except ValueError:
                    is_upgrade = True

            if not is_upgrade and existing_download.status in ['pending', 'downloading', 'monitoring']:
                return JsonResponse({
                    'error': f'Episode already has an active download: {existing_download.torrent_title}',
                    'existing_download': {
                        'status': existing_download.status,
                        'title': existing_download.torrent_title,
                        'quality': existing_download.quality,
                    }
                }, status=409)
            elif is_upgrade:
                logger.info(f"Quality upgrade detected for episode {episode}: {existing_download.quality} -> {new_quality_str}")
            else:
                logger.info(f"Replacing completed download for episode {episode}")


        # Initialize Real Debrid service
        try:
            from .real_debrid_service import RealDebridService, RealDebridAPIError
            rd_service = RealDebridService()
        except RealDebridAPIError as e:
            # Create a failed download status record
            download_status, created = DownloadStatus.objects.update_or_create(
                episode=episode,
                defaults={
                    'status': 'failed',
                    'torrent_title': title,
                    'error_message': f'Real Debrid configuration error: {e}',
                    'quality': data.get('quality', ''),
                    'indexer': data.get('indexer', ''),
                    'retry_count': 0,
                }
            )
            return JsonResponse({'error': f'Real Debrid configuration error: {e}'}, status=500)

        # Check if torrent is cached using the new approach (handle both magnet URLs and torrent file URLs)
        torrent_url = magnet_url if magnet_url else download_url
        is_cached, cache_info = rd_service.is_torrent_cached(torrent_url, title)

        if not is_cached:
            error_msg = cache_info.get('error', f'Torrent "{title}" is not cached on Real Debrid. Try another torrent.')

            # Create a failed download status record
            download_status, created = DownloadStatus.objects.update_or_create(
                episode=episode,
                defaults={
                    'status': 'failed',
                    'source': 'manual',  # Manual search download
                    'torrent_title': title,
                    'torrent_hash': cache_info.get('hash', ''),
                    'magnet_url': cache_info.get('magnet_url', magnet_url),
                    'download_url': download_url,
                    'real_debrid_id': '',
                    'quality': data.get('quality', ''),
                    'indexer': data.get('indexer', ''),
                    'size_bytes': data.get('size', 0),
                    'seeders': data.get('seeders', 0),
                    'error_message': error_msg,
                    'retry_count': 0,
                }
            )

            return JsonResponse({
                'success': False,
                'cached': False,
                'message': error_msg,
                'title': title
            })

        # Get the magnet URL and torrent ID (already added during cache check)
        final_magnet_url = cache_info.get('magnet_url', magnet_url)
        torrent_id = cache_info.get('torrent_id')

        # Torrent is cached and already added to Real Debrid
        if not torrent_id:
            # Create a failed download status record
            download_status, created = DownloadStatus.objects.update_or_create(
                episode=episode,
                defaults={
                    'status': 'failed',
                    'source': 'manual',  # Manual search download
                    'torrent_title': title,
                    'torrent_hash': cache_info.get('hash', ''),
                    'magnet_url': final_magnet_url,
                    'download_url': download_url,
                    'real_debrid_id': '',
                    'quality': data.get('quality', ''),
                    'indexer': data.get('indexer', ''),
                    'size_bytes': data.get('size', 0),
                    'seeders': data.get('seeders', 0),
                    'error_message': 'Failed to add torrent to Real Debrid',
                    'retry_count': 0,
                }
            )

            return JsonResponse({
                'success': False,
                'error': 'Failed to add torrent to Real Debrid'
            })

        # Store old download ID for cleanup after new download completes
        old_real_debrid_id = ''
        if existing_download and existing_download.real_debrid_id:
            old_real_debrid_id = existing_download.real_debrid_id
            logger.info(f"Quality upgrade detected for episode {episode.season.tv_show.title} S{episode.season.season_number:02d}E{episode.episode_number:02d}: {existing_download.quality} -> {data.get('quality', '')}")
            logger.info(f"Will clean up old download after new one completes: {existing_download.torrent_title}")

        # Create or update download status
        download_status, created = DownloadStatus.objects.update_or_create(
            episode=episode,
            defaults={
                'status': 'pending',
                'source': 'manual',  # Manual search download
                'torrent_title': title,
                'torrent_hash': cache_info.get('hash', ''),
                'magnet_url': final_magnet_url,
                'download_url': download_url,
                'real_debrid_id': torrent_id,
                'quality': data.get('quality', ''),
                'indexer': data.get('indexer', ''),
                'cleanup_after_completion': old_real_debrid_id,
                'size_bytes': data.get('size', 0),
                'seeders': data.get('seeders', 0),
                'error_message': '',
                'retry_count': 0,
            }
        )

        logger.info(f"Successfully added cached torrent to Real Debrid: {title} (ID: {torrent_id})")

        return JsonResponse({
            'success': True,
            'cached': True,
            'message': f'Torrent "{title}" added to Real Debrid successfully. Monitoring for completion.',
            'title': title,
            'torrent_id': torrent_id,
            'download_status_id': download_status.id
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Failed to process download request: {e}")

        # Try to create a failed download status if we have episode info
        try:
            if 'episode_id' in locals():
                download_status, created = DownloadStatus.objects.update_or_create(
                    episode=episode,
                    defaults={
                        'status': 'failed',
                        'source': 'manual',  # Manual search download
                        'torrent_title': data.get('title', 'Unknown'),
                        'error_message': f'Download request failed: {str(e)}',
                        'retry_count': 0,
                    }
                )
        except:
            pass  # Don't let this fail the error response

        return JsonResponse({
            'error': f'Failed to process download request: {str(e)}'
        }, status=500)


def jackett_test_search(request):
    """Test search endpoint for debugging"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get test parameters
        query = request.GET.get('q', 'test')

        # Initialize Jackett service
        jackett_service = JackettService()

        # Test basic search
        results = jackett_service.search(query, indexer='all', category='5000')

        return JsonResponse({
            'success': True,
            'query': query,
            'results_count': len(results),
            'results': [result.to_dict() for result in results[:5]],  # First 5 results
            'debug_info': {
                'base_url': jackett_service.base_url,
                'api_key_configured': bool(jackett_service.api_key)
            }
        })

    except JackettAPIError as e:
        logger.error(f"Jackett test search failed: {e}")
        return JsonResponse({
            'error': f'Jackett search failed: {str(e)}',
            'debug_info': {
                'base_url': getattr(jackett_service, 'base_url', 'Not initialized'),
                'api_key_configured': bool(getattr(jackett_service, 'api_key', False))
            }
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in test search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_search_season(request):
    """Search for a complete season using Jackett"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title', '').strip()
        season = request.GET.get('season', '')
        indexer = request.GET.get('indexer', 'all')
        filter_results = request.GET.get('filter', 'true').lower() == 'true'

        # Validate parameters
        if not show_title:
            return JsonResponse({'error': 'show_title is required'}, status=400)

        if not season:
            return JsonResponse({'error': 'season is required'}, status=400)

        try:
            season_num = int(season)
        except ValueError:
            return JsonResponse({'error': 'season must be an integer'}, status=400)

        # Initialize Jackett service
        jackett_service = JackettService()

        # Perform season search
        results = jackett_service.search_season(show_title, season_num, indexer, filter_results)

        # Get quality profile for scoring (try to find the show's profile)
        quality_profile = None
        try:
            from .models import TVShow
            tv_show = TVShow.objects.filter(title__icontains=show_title).first()
            if tv_show and tv_show.quality_profile:
                quality_profile = tv_show.quality_profile
        except Exception as e:
            logger.debug(f"Could not get quality profile for scoring: {e}")

        # Calculate scores for all results
        results = jackett_service.calculate_scores_for_results(results, quality_profile)

        # Sort by score (highest first), then by seeders
        results.sort(key=lambda x: (x.score, x.seeders), reverse=True)

        # Convert results to JSON-serializable format
        results_data = [result.to_dict() for result in results]

        return JsonResponse({
            'success': True,
            'results': results_data,
            'total_results': len(results_data),
            'search_query': f"{show_title} Season {season_num}",
            'search_type': 'season',
            'filtered': filter_results,
        })

    except JackettAPIError as e:
        logger.error(f"Jackett season search failed: {e}")
        return JsonResponse({
            'error': f'Jackett season search failed: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in Jackett season search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def api_profiles_list(request):
    """API endpoint to get list of active profiles"""
    try:
        from .models import QualityProfile

        profiles = QualityProfile.objects.filter(is_active=True).order_by('name')

        profiles_data = []
        for profile in profiles:
            profiles_data.append({
                'id': profile.id,
                'name': profile.name,
                'description': profile.description,
                'is_default': profile.is_default,
                'allow_upgrades': profile.allow_upgrades,
                'upgrade_until_quality': profile.upgrade_until_quality.name if profile.upgrade_until_quality else None
            })

        return JsonResponse({
            'success': True,
            'profiles': profiles_data
        })

    except Exception as e:
        logger.error(f"Failed to get profiles list: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def jackett_auto_search_episode(request):
    """Automatically search for best episode torrent based on quality profile"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title')
        season = request.GET.get('season')
        episode = request.GET.get('episode')
        show_id = request.GET.get('show_id')

        if not all([show_title, season, episode]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)

        try:
            season = int(season)
            episode = int(episode)
        except ValueError:
            return JsonResponse({'error': 'Season and episode must be integers'}, status=400)

        # Get the show and its quality profile
        quality_profile = None
        if show_id:
            try:
                from .models import TVShow
                tv_show = TVShow.objects.select_related('quality_profile').get(id=show_id)
                quality_profile = tv_show.quality_profile
            except TVShow.DoesNotExist:
                pass

        # Initialize Jackett service
        jackett_service = JackettService()

        # Search for episode
        results = jackett_service.search_episode(show_title, season, episode)

        if not results:
            return JsonResponse({
                'success': False,
                'error': 'No torrents found for this episode'
            })

        # Filter results for this specific episode
        filtered_results = jackett_service._filter_episode_results(results, show_title, season, episode)

        if not filtered_results:
            return JsonResponse({
                'success': False,
                'error': 'No matching torrents found for this episode'
            })

        # Select best torrent based on quality profile
        best_torrent = jackett_service.select_best_torrent(filtered_results, quality_profile)

        if not best_torrent:
            return JsonResponse({
                'success': False,
                'error': 'No suitable torrent found based on quality profile'
            })

        return JsonResponse({
            'success': True,
            'message': f'Best torrent selected for {show_title} S{season:02d}E{episode:02d}',
            'torrent': {
                'title': best_torrent.title,
                'quality': best_torrent.quality,
                'size': best_torrent.size,
                'seeders': best_torrent.seeders,
                'leechers': best_torrent.leechers,
                'indexer': best_torrent.indexer,
                'download_url': best_torrent.download_url,
                'magnet_url': best_torrent.magnet_url
            },
            'profile_used': quality_profile.name if quality_profile else 'No profile (default selection)',
            'total_results': len(results),
            'filtered_results': len(filtered_results)
        })

    except JackettAPIError as e:
        logger.error(f"Jackett API error in auto search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Jackett API error: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in auto search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_auto_download_episode(request):
    """Automatically search and download best episode torrent with Real Debrid integration"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title')
        season = request.GET.get('season')
        episode = request.GET.get('episode')
        show_id = request.GET.get('show_id')

        if not all([show_title, season, episode]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)

        try:
            season = int(season)
            episode = int(episode)
        except ValueError:
            return JsonResponse({'error': 'Season and episode must be integers'}, status=400)

        # Get the episode object
        try:
            from .models import TVShow, Episode, DownloadStatus
            tv_show = TVShow.objects.select_related('quality_profile').get(id=show_id)
            episode_obj = Episode.objects.get(
                season__tv_show=tv_show,
                season__season_number=season,
                episode_number=episode
            )
        except (TVShow.DoesNotExist, Episode.DoesNotExist):
            return JsonResponse({'error': 'Episode not found'}, status=404)

        # Check if there's already an active download for this episode
        existing_download = None
        try:
            existing_download = DownloadStatus.objects.get(
                episode=episode_obj,
                status__in=['pending', 'downloading', 'monitoring']
            )
            # For auto-download, we don't want to interfere with active downloads
            return JsonResponse({
                'success': False,
                'error': f'Episode already has an active download: {existing_download.torrent_title}',
                'existing_download': {
                    'status': existing_download.status,
                    'title': existing_download.torrent_title,
                    'quality': existing_download.quality,
                }
            })
        except DownloadStatus.DoesNotExist:
            pass
        except DownloadStatus.MultipleObjectsReturned:
            # If there are multiple active downloads, don't proceed
            return JsonResponse({
                'success': False,
                'error': 'Episode has multiple active downloads'
            })

        # Check for completed downloads to potentially upgrade
        completed_download = None
        try:
            completed_download = DownloadStatus.objects.filter(
                episode=episode_obj,
                status='completed'
            ).order_by('-completed_at').first()
        except DownloadStatus.DoesNotExist:
            pass

        # Initialize services
        jackett_service = JackettService()

        try:
            from .real_debrid_service import RealDebridService, RealDebridAPIError
            rd_service = RealDebridService()
        except RealDebridAPIError as e:
            return JsonResponse({'error': f'Real Debrid configuration error: {e}'}, status=500)

        # Search for episode
        results = jackett_service.search_episode(show_title, season, episode)
        if not results:
            return JsonResponse({
                'success': False,
                'error': 'No torrents found for this episode'
            })

        # Filter results for this specific episode
        filtered_results = jackett_service._filter_episode_results(results, show_title, season, episode)
        if not filtered_results:
            return JsonResponse({
                'success': False,
                'error': 'No matching torrents found for this episode'
            })

        # Try each torrent in order of score until we find a cached one
        quality_profile = tv_show.quality_profile

        # Calculate scores for all results using quality profile
        filtered_results = jackett_service.calculate_scores_for_results(filtered_results, quality_profile)

        # Sort results by score (highest first), then by seeders
        sorted_results = sorted(
            filtered_results,
            key=lambda x: (x.score, x.seeders),
            reverse=True
        )

        logger.info(f"Auto-download for {show_title} S{season:02d}E{episode:02d}: Found {len(sorted_results)} torrents, sorted by score")

        successful_torrent = None
        tried_count = 0

        for torrent in sorted_results:
            tried_count += 1

            logger.info(f"Auto-download attempt #{tried_count}: {torrent.title} (Score: {torrent.score}, Seeders: {torrent.seeders})")

            # Check if this torrent is cached using the new approach (try magnet URL first, then download URL)
            torrent_url = torrent.magnet_url if torrent.magnet_url else torrent.download_url
            is_cached, cache_info = rd_service.is_torrent_cached(torrent_url, torrent.title)

            if is_cached:
                # Get the final magnet URL and torrent ID (already added during cache check)
                final_magnet_url = cache_info.get('magnet_url', torrent.magnet_url)
                torrent_id = cache_info.get('torrent_id')

                if torrent_id:
                    # Check if this is a quality upgrade using profile rules
                    is_upgrade = False
                    if completed_download:
                        # Get quality objects for comparison
                        existing_download_quality = completed_download.get_quality_object()

                        # Create quality object for new torrent
                        new_torrent_quality = None
                        if torrent.quality:
                            # Try to determine source and codec from torrent title
                            title_upper = torrent.title.upper()
                            source = 'Unknown'
                            codec = 'Unknown'

                            if 'REMUX' in title_upper:
                                source = 'REMUX'
                            elif 'BLURAY' in title_upper or 'BLU-RAY' in title_upper:
                                source = 'BluRay'
                            elif 'WEB-DL' in title_upper:
                                source = 'WEB-DL'
                            elif 'WEBRIP' in title_upper:
                                source = 'WEBRip'
                            elif 'WEB' in title_upper:
                                source = 'WEB'
                            elif 'HDTV' in title_upper:
                                source = 'HDTV'

                            if 'AV1' in title_upper:
                                codec = 'AV1'
                            elif 'H265' in title_upper or 'HEVC' in title_upper:
                                codec = 'h265'
                            elif 'H264' in title_upper or 'AVC' in title_upper:
                                codec = 'h264'

                            try:
                                new_torrent_quality, _ = MediaQuality.objects.get_or_create(
                                    resolution=torrent.quality,
                                    source=source,
                                    codec=codec,
                                    defaults={}
                                )
                            except Exception:
                                pass

                        # Use quality profile for comparison
                        quality_profile = tv_show.quality_profile if hasattr(tv_show, 'quality_profile') else None

                        if new_torrent_quality and existing_download_quality:
                            is_upgrade = new_torrent_quality.is_significantly_better_than(existing_download_quality, quality_profile)
                        elif new_torrent_quality:
                            is_upgrade = True
                        else:
                            # Fallback to simple comparison
                            quality_order = ['480p', '720p', '1080p', '4K', '2160p']
                            try:
                                new_idx = quality_order.index(torrent.quality)
                                existing_idx = quality_order.index(completed_download.quality)
                                is_upgrade = new_idx > existing_idx
                            except ValueError:
                                is_upgrade = True

                    # Only proceed if this is a new download or a quality upgrade
                    if not completed_download or is_upgrade:
                        # Store old download ID for cleanup after new download completes
                        old_real_debrid_id = ''
                        if completed_download and completed_download.real_debrid_id and is_upgrade:
                            old_real_debrid_id = completed_download.real_debrid_id
                            logger.info(f"Quality upgrade detected for episode {show_title} S{season:02d}E{episode:02d}: {completed_download.quality} -> {torrent.quality}")
                            logger.info(f"Will clean up old download after new one completes: {completed_download.torrent_title}")

                        # Success! Create download status
                        download_status, created = DownloadStatus.objects.update_or_create(
                            episode=episode_obj,
                            defaults={
                                'status': 'pending',
                                'source': 'auto',  # Automatic search download
                                'torrent_title': torrent.title,
                                'torrent_hash': cache_info.get('hash', ''),
                                'magnet_url': final_magnet_url,
                                'download_url': torrent.download_url,
                                'cleanup_after_completion': old_real_debrid_id,
                                'real_debrid_id': torrent_id,
                                'quality': torrent.quality,
                                'indexer': torrent.indexer,
                                'size_bytes': torrent.size,
                                'seeders': torrent.seeders,
                                'error_message': '',
                                'retry_count': 0,
                            }
                        )

                        successful_torrent = torrent
                        break
                    else:
                        # Not an upgrade, clean up the torrent we just added and continue
                        logger.info(f"Auto-download: {torrent.quality} is not better than existing {completed_download.quality}, skipping")
                        rd_service.delete_torrent(torrent_id)
                        continue

            # If we've tried 5 torrents without success, stop
            if tried_count >= 5:
                break

        if successful_torrent:
            logger.info(f"Successfully started download for {show_title} S{season:02d}E{episode:02d}: {successful_torrent.title} (Score: {successful_torrent.score})")

            return JsonResponse({
                'success': True,
                'message': f'Download started for {show_title} S{season:02d}E{episode:02d}',
                'torrent': {
                    'title': successful_torrent.title,
                    'quality': successful_torrent.quality,
                    'size': successful_torrent.size_formatted,
                    'seeders': successful_torrent.seeders,
                    'indexer': successful_torrent.indexer,
                    'score': successful_torrent.score,
                },
                'profile_used': quality_profile.name if quality_profile else 'No profile (default selection)',
                'total_results': len(results),
                'filtered_results': len(filtered_results),
                'tried_count': tried_count
            })
        else:
            return JsonResponse({
                'success': False,
                'error': f'No cached torrents found after trying {tried_count} options. All torrents need to be downloaded first.',
                'total_results': len(results),
                'filtered_results': len(filtered_results),
                'tried_count': tried_count
            })

    except JackettAPIError as e:
        logger.error(f"Jackett API error in auto download: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Jackett API error: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in auto download: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_auto_search_episode_old(request):
    """Legacy auto search function - kept for compatibility"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title')
        season = request.GET.get('season')
        episode = request.GET.get('episode')
        show_id = request.GET.get('show_id')

        if not all([show_title, season, episode]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)

        try:
            season = int(season)
            episode = int(episode)
        except ValueError:
            return JsonResponse({'error': 'Season and episode must be integers'}, status=400)

        # Get the show and its quality profile
        quality_profile = None
        if show_id:
            try:
                from .models import TVShow
                tv_show = TVShow.objects.select_related('quality_profile').get(id=show_id)
                quality_profile = tv_show.quality_profile
            except TVShow.DoesNotExist:
                pass

        # Initialize Jackett service
        jackett_service = JackettService()

        # Search for episode
        results = jackett_service.search_episode(show_title, season, episode)

        if not results:
            return JsonResponse({
                'success': False,
                'error': 'No torrents found for this episode'
            })

        # Filter results for this specific episode
        filtered_results = jackett_service._filter_episode_results(results, show_title, season, episode)

        if not filtered_results:
            return JsonResponse({
                'success': False,
                'error': 'No matching torrents found for this episode'
            })

        # Select best torrent based on quality profile
        best_torrent = jackett_service.select_best_torrent(filtered_results, quality_profile)

        if not best_torrent:
            return JsonResponse({
                'success': False,
                'error': 'No suitable torrent found based on quality profile'
            })

        return JsonResponse({
            'success': True,
            'message': f'Best torrent selected for {show_title} S{season:02d}E{episode:02d}',
            'torrent': {
                'title': best_torrent.title,
                'quality': best_torrent.quality,
                'size': best_torrent.size,
                'seeders': best_torrent.seeders,
                'leechers': best_torrent.leechers,
                'indexer': best_torrent.indexer,
                'download_url': best_torrent.download_url,
                'magnet_url': best_torrent.magnet_url
            },
            'profile_used': quality_profile.name if quality_profile else 'No profile (default selection)',
            'total_results': len(results),
            'filtered_results': len(filtered_results)
        })

    except JackettAPIError as e:
        logger.error(f"Jackett API error in auto search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Jackett API error: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in auto search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_auto_search_season(request):
    """Automatically search for best season torrent based on quality profile"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title')
        season = request.GET.get('season')
        show_id = request.GET.get('show_id')

        if not all([show_title, season]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)

        try:
            season = int(season)
        except ValueError:
            return JsonResponse({'error': 'Season must be an integer'}, status=400)

        # Get the show and its quality profile
        quality_profile = None
        if show_id:
            try:
                from .models import TVShow
                tv_show = TVShow.objects.select_related('quality_profile').get(id=show_id)
                quality_profile = tv_show.quality_profile
            except TVShow.DoesNotExist:
                pass

        # Initialize Jackett service
        jackett_service = JackettService()

        # Search for season
        results = jackett_service.search_season(show_title, season)

        if not results:
            return JsonResponse({
                'success': False,
                'error': 'No torrents found for this season'
            })

        # Filter results for this specific season
        filtered_results = jackett_service._filter_season_results(results, show_title, season)

        if not filtered_results:
            return JsonResponse({
                'success': False,
                'error': 'No matching season packs found'
            })

        # Select best torrent based on quality profile
        best_torrent = jackett_service.select_best_torrent(filtered_results, quality_profile)

        if not best_torrent:
            return JsonResponse({
                'success': False,
                'error': 'No suitable season pack found based on quality profile'
            })

        return JsonResponse({
            'success': True,
            'message': f'Best season pack selected for {show_title} Season {season}',
            'torrent': {
                'title': best_torrent.title,
                'quality': best_torrent.quality,
                'size': best_torrent.size,
                'seeders': best_torrent.seeders,
                'leechers': best_torrent.leechers,
                'indexer': best_torrent.indexer,
                'download_url': best_torrent.download_url,
                'magnet_url': best_torrent.magnet_url
            },
            'profile_used': quality_profile.name if quality_profile else 'No profile (default selection)',
            'total_results': len(results),
            'filtered_results': len(filtered_results)
        })

    except JackettAPIError as e:
        logger.error(f"Jackett API error in auto season search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Jackett API error: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in auto season search: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def real_debrid_test(request):
    """Test Real Debrid API connection and torrent conversion"""
    try:
        from .real_debrid_service import RealDebridService, RealDebridAPIError

        rd_service = RealDebridService()

        results = {}

        # Test connection
        if rd_service.test_connection():
            results['connection'] = 'Real Debrid API connection successful!'
        else:
            results['connection'] = 'Real Debrid API connection failed'

        # Test torrent file to magnet conversion with a sample URL
        test_torrent_url = request.GET.get('test_torrent_url')
        if test_torrent_url:
            magnet_url = rd_service.torrent_file_to_magnet(test_torrent_url, 'Test Torrent')
            if magnet_url:
                results['conversion'] = f'Successfully converted torrent to magnet: {magnet_url[:100]}...'
            else:
                results['conversion'] = 'Failed to convert torrent file to magnet URL'
        else:
            results['conversion'] = 'No test torrent URL provided. Add ?test_torrent_url=<url> to test conversion.'

        return JsonResponse({
            'success': True,
            'results': results
        })

    except RealDebridAPIError as e:
        return JsonResponse({
            'success': False,
            'error': f'Real Debrid configuration error: {e}'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {e}'
        })


def episode_details(request, episode_id):
    """Get detailed information about an episode including download status and file info"""
    try:
        episode = get_object_or_404(Episode, id=episode_id)

        # Get download status if available
        download_status = None
        download_info = None

        if hasattr(episode, 'download_status') and episode.download_status:
            download_status = episode.download_status
            download_info = {
                'status': download_status.status,
                'torrent_title': download_status.torrent_title,
                'torrent_hash': download_status.torrent_hash,
                'quality': download_status.quality,
                'indexer': download_status.indexer,
                'size_formatted': download_status.size_formatted,
                'seeders': download_status.seeders,
                'started_at': download_status.started_at.isoformat() if download_status.started_at else None,
                'completed_at': download_status.completed_at.isoformat() if download_status.completed_at else None,
                'error_message': download_status.error_message,
                'retry_count': download_status.retry_count,
                'real_debrid_id': download_status.real_debrid_id,
            }

            # Get Real Debrid file info if available
            if download_status.real_debrid_id and download_status.status in ['completed', 'monitoring']:
                try:
                    from .real_debrid_service import RealDebridService
                    rd_service = RealDebridService()

                    # Get torrent info from Real Debrid
                    torrent_info = rd_service.get_torrent_info(download_status.real_debrid_id)
                    if torrent_info:
                        download_info['real_debrid_info'] = {
                            'status': torrent_info.get('status'),
                            'progress': torrent_info.get('progress', 0),
                            'files': torrent_info.get('files', []),
                            'links': torrent_info.get('links', []),
                        }
                except Exception as e:
                    logger.error(f"Failed to get Real Debrid info for episode {episode_id}: {e}")

        # Episode basic info
        episode_data = {
            'id': episode.id,
            'episode_number': episode.episode_number,
            'name': episode.name,
            'overview': episode.overview,
            'air_date': episode.air_date.isoformat() if episode.air_date else None,
            'runtime': episode.runtime,

            'quality': {
                'resolution': episode.quality.resolution if episode.quality else None,
                'source': episode.quality.source if episode.quality else None,
                'codec': episode.quality.codec if episode.quality else None,
            } if episode.quality else None,
            'season': {
                'season_number': episode.season.season_number,
                'tv_show': {
                    'title': episode.season.tv_show.title,
                    'id': episode.season.tv_show.id,
                }
            },
            'download_info': download_info,
        }

        return JsonResponse({
            'success': True,
            'episode': episode_data
        })

    except Exception as e:
        logger.error(f"Failed to get episode details for {episode_id}: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Failed to get episode details: {str(e)}'
        }, status=500)


def remove_episode_download(request, episode_id):
    """Remove episode download from database and Real Debrid"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        episode = get_object_or_404(Episode, id=episode_id)

        # Check if episode has download status
        if not hasattr(episode, 'download_status') or not episode.download_status:
            return JsonResponse({
                'success': False,
                'error': 'Episode has no download to remove'
            })

        download_status = episode.download_status
        real_debrid_id = download_status.real_debrid_id

        # Remove from Real Debrid if we have a torrent ID
        if real_debrid_id:
            try:
                from .real_debrid_service import RealDebridService
                rd_service = RealDebridService()

                success = rd_service.delete_torrent(real_debrid_id)
                if success:
                    logger.info(f"Successfully removed torrent {real_debrid_id} from Real Debrid")
                else:
                    logger.warning(f"Failed to remove torrent {real_debrid_id} from Real Debrid")

            except Exception as e:
                logger.error(f"Error removing torrent from Real Debrid: {e}")

        # Remove download status from database
        torrent_title = download_status.torrent_title
        download_status.delete()

        # Reset episode quality
        episode.quality = None
        episode.save()

        logger.info(f"Removed download for {episode}: {torrent_title}")

        return JsonResponse({
            'success': True,
            'message': f'Successfully removed download for {episode.season.tv_show.title} S{episode.season.season_number:02d}E{episode.episode_number:02d}',
            'removed_from_real_debrid': bool(real_debrid_id),
            'torrent_title': torrent_title
        })

    except Exception as e:
        logger.error(f"Failed to remove episode download for {episode_id}: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Failed to remove download: {str(e)}'
        }, status=500)
