from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class MediaQuality(models.Model):
    """Model to store quality information for media content"""
    resolution = models.Char<PERSON>ield(max_length=20, help_text="e.g., 1080p, 720p, 4K")
    source = models.CharField(max_length=50, help_text="e.g., BluRay, WEB-DL, HDTV")
    codec = models.CharField(max_length=20, help_text="e.g., x264, x265, AV1")

    class Meta:
        verbose_name_plural = "Media Qualities"
        unique_together = ['resolution', 'source', 'codec']

    def __str__(self):
        return f"{self.resolution} {self.source} {self.codec}"

    def get_resolution_priority(self):
        """Get numeric priority for resolution (higher = better)"""
        resolution_priorities = {
            '4K': 4000,
            '2160p': 4000,
            '1080p': 1080,
            '720p': 720,
            '480p': 480,
            'SD': 240,
            'Unknown': 0,
        }
        return resolution_priorities.get(self.resolution, 0)

    def get_source_priority(self):
        """Get numeric priority for source (higher = better)"""
        source_priorities = {
            'REMUX': 1000,
            'BluRay': 900,
            'WEB-DL': 800,
            'WEBRip': 700,
            'WEB': 600,
            'HDTV': 500,
            'DVD': 400,
            'Unknown': 0,
        }
        return source_priorities.get(self.source, 0)

    def get_codec_priority(self):
        """Get numeric priority for codec (higher = better)"""
        codec_priorities = {
            'AV1': 300,
            'h265': 200,
            'HEVC': 200,
            'h264': 100,
            'AVC': 100,
            'MPEG2': 50,
            'Unknown': 0,
        }
        return codec_priorities.get(self.codec, 0)

    def get_total_score(self):
        """Get total quality score for comparison"""
        return self.get_resolution_priority() + self.get_source_priority() + self.get_codec_priority()

    def is_better_than(self, other_quality, quality_profile=None):
        """Check if this quality is better than another quality, respecting quality profile"""
        if not other_quality:
            return True

        # If we have a quality profile, use it for comparison
        if quality_profile:
            return self._is_better_than_with_profile(other_quality, quality_profile)

        # Fallback to simple score comparison
        return self.get_total_score() > other_quality.get_total_score()

    def is_significantly_better_than(self, other_quality, quality_profile=None, threshold=100):
        """Check if this quality is significantly better (to avoid minor upgrades)"""
        if not other_quality:
            return True

        # If we have a quality profile, use it for comparison
        if quality_profile:
            return self._is_significantly_better_than_with_profile(other_quality, quality_profile, threshold)

        # Fallback to simple score comparison
        return self.get_total_score() > (other_quality.get_total_score() + threshold)

    def _is_better_than_with_profile(self, other_quality, quality_profile):
        """Check if this quality is better using quality profile rules"""
        try:
            # Check if upgrades are allowed at all
            if not quality_profile.allow_upgrades:
                return False

            # Check if we've reached the upgrade until quality
            if quality_profile.upgrade_until_quality:
                # If the current quality has already reached the upgrade limit, don't upgrade
                if self._matches_quality_definition(other_quality, quality_profile.upgrade_until_quality):
                    return False

            # Get profile items for both qualities
            current_item = self._get_profile_item(quality_profile)
            other_item = other_quality._get_profile_item(quality_profile)

            # If either quality is not allowed in the profile, use fallback comparison
            if not current_item or not other_item:
                return self.get_total_score() > other_quality.get_total_score()

            # If current quality is not allowed, don't upgrade to it
            if not current_item.allowed:
                return False

            # Compare based on profile order (higher order = better)
            return current_item.order > other_item.order

        except Exception:
            # Fallback to simple comparison if profile logic fails
            return self.get_total_score() > other_quality.get_total_score()

    def _is_significantly_better_than_with_profile(self, other_quality, quality_profile, threshold=1):
        """Check if this quality is significantly better using quality profile rules"""
        try:
            # Check if upgrades are allowed at all
            if not quality_profile.allow_upgrades:
                return False

            # Check if we've reached the upgrade until quality
            if quality_profile.upgrade_until_quality:
                # If the current quality has already reached the upgrade limit, don't upgrade
                if self._matches_quality_definition(other_quality, quality_profile.upgrade_until_quality):
                    return False

            # Get profile items for both qualities
            current_item = self._get_profile_item(quality_profile)
            other_item = other_quality._get_profile_item(quality_profile)

            # If either quality is not allowed in the profile, use fallback comparison
            if not current_item or not other_item:
                return self.get_total_score() > (other_quality.get_total_score() + 100)

            # If current quality is not allowed, don't upgrade to it
            if not current_item.allowed:
                return False

            # For profile-based comparison, use a smaller threshold (order difference)
            # since the profile order already represents user preferences
            return current_item.order > (other_item.order + threshold)

        except Exception:
            # Fallback to simple comparison if profile logic fails
            return self.get_total_score() > (other_quality.get_total_score() + 100)

    def _get_profile_item(self, quality_profile):
        """Get the QualityProfileItem for this quality in the given profile"""
        try:
            # Try to find a matching quality definition first
            quality_def = self._find_matching_quality_definition()
            if quality_def:
                return quality_profile.quality_items.filter(quality=quality_def).first()
            return None
        except Exception:
            return None

    def _find_matching_quality_definition(self):
        """Find a QualityDefinition that matches this MediaQuality"""
        try:
            from media_requests.models import QualityDefinition

            # Try exact match first
            quality_def = QualityDefinition.objects.filter(
                resolution=self.resolution,
                source=self.source
            ).first()

            if quality_def:
                return quality_def

            # Try resolution-only match
            quality_def = QualityDefinition.objects.filter(
                resolution=self.resolution
            ).first()

            return quality_def
        except Exception:
            return None

    def _matches_quality_definition(self, other_quality, quality_definition):
        """Check if a quality matches a quality definition"""
        try:
            # Check if the other quality matches the definition
            if quality_definition.resolution == other_quality.resolution:
                if quality_definition.source:
                    return quality_definition.source == other_quality.source
                return True
            return False
        except Exception:
            return False


class MediaRequest(models.Model):
    """Model to store Overseerr media request metadata"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('declined', 'Declined'),
        ('available', 'Available'),
    ]

    overseerr_id = models.IntegerField(unique=True, help_text="ID from Overseerr API")
    media_type = models.CharField(max_length=10, choices=[('tv', 'TV Show'), ('movie', 'Movie')])
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_by = models.CharField(max_length=100)
    requested_at = models.DateTimeField()
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-requested_at']

    def __str__(self):
        return f"Request #{self.overseerr_id} - {self.media_type}"


class TVShow(models.Model):
    """Model to store TV show information"""

    # Basic show information
    tmdb_id = models.IntegerField(unique=True, help_text="TMDB ID")
    title = models.CharField(max_length=200)
    overview = models.TextField(blank=True)
    poster_path = models.CharField(max_length=200, blank=True)
    backdrop_path = models.CharField(max_length=200, blank=True)
    first_air_date = models.DateField(null=True, blank=True)
    last_air_date = models.DateField(null=True, blank=True)

    # Status
    status = models.CharField(max_length=50, blank=True)  # e.g., "Ended", "Returning Series"

    # Plex integration
    plex_rating_key = models.CharField(max_length=50, blank=True, help_text="Plex rating key for direct API access")

    # Relationships
    media_request = models.OneToOneField(MediaRequest, on_delete=models.CASCADE, null=True, blank=True)
    quality_profile = models.ForeignKey('QualityProfile', on_delete=models.SET_NULL, null=True, blank=True, help_text="Quality profile for this show")

    # Direct addition fields (when not from Overseerr request)
    added_by = models.CharField(max_length=100, blank=True, help_text="User who added this show directly")
    added_at = models.DateTimeField(null=True, blank=True, help_text="When this show was added directly")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['title']

    def __str__(self):
        return self.title

    def get_total_seasons(self):
        return self.seasons.exclude(season_number=0).count()

    def get_available_seasons(self):
        return self.seasons.exclude(season_number=0).filter(episodes__quality__isnull=False).distinct().count()

    def get_total_episodes(self):
        return sum(season.episodes.count() for season in self.seasons.exclude(season_number=0))

    def get_available_episodes(self):
        return sum(season.episodes.filter(quality__isnull=False).count() for season in self.seasons.exclude(season_number=0))

    def get_primary_quality(self):
        """Get the most common quality for this show"""
        from django.db.models import Count

        # Get the most common quality among episodes with quality info
        quality_counts = (
            Episode.objects
            .filter(season__tv_show=self, quality__isnull=False)
            .values('quality__resolution')
            .annotate(count=Count('quality__resolution'))
            .order_by('-count')
            .first()
        )

        if quality_counts:
            return quality_counts['quality__resolution']
        return None


class Season(models.Model):
    """Model to store season information for TV shows"""

    # Basic season information
    tv_show = models.ForeignKey(TVShow, on_delete=models.CASCADE, related_name='seasons')
    season_number = models.IntegerField()
    name = models.CharField(max_length=200, blank=True)
    overview = models.TextField(blank=True)
    poster_path = models.CharField(max_length=200, blank=True)
    air_date = models.DateField(null=True, blank=True)

    # Quality
    quality = models.ForeignKey(MediaQuality, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['season_number']
        unique_together = ['tv_show', 'season_number']

    def __str__(self):
        return f"{self.tv_show.title} - Season {self.season_number}"

    def get_total_episodes(self):
        return self.episodes.count()

    def get_available_episodes(self):
        return self.episodes.filter(quality__isnull=False).count()


class Episode(models.Model):
    """Model to store episode information"""

    # Basic episode information
    season = models.ForeignKey(Season, on_delete=models.CASCADE, related_name='episodes')
    episode_number = models.IntegerField()
    name = models.CharField(max_length=200, blank=True)
    overview = models.TextField(blank=True)
    still_path = models.CharField(max_length=200, blank=True)
    air_date = models.DateField(null=True, blank=True)
    runtime = models.IntegerField(null=True, blank=True, help_text="Runtime in minutes")

    # Quality
    quality = models.ForeignKey(MediaQuality, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['episode_number']
        unique_together = ['season', 'episode_number']

    def __str__(self):
        return f"{self.season.tv_show.title} S{self.season.season_number:02d}E{self.episode_number:02d} - {self.name}"

    @property
    def tv_show(self):
        return self.season.tv_show


# Quality Profile Models

class QualityDefinition(models.Model):
    """Defines available quality levels and their properties"""

    name = models.CharField(max_length=50, unique=True)
    resolution = models.CharField(max_length=20)  # e.g., "1080p", "720p", "4K"
    source = models.CharField(max_length=20, blank=True)  # e.g., "WEB-DL", "BluRay", "HDTV"
    priority = models.IntegerField(default=0, help_text="Higher number = higher priority")
    min_size_mb = models.IntegerField(default=0, help_text="Minimum file size in MB")
    max_size_mb = models.IntegerField(default=0, help_text="Maximum file size in MB (0 = unlimited)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'name']

    def __str__(self):
        if self.source:
            return f"{self.resolution} {self.source}"
        return self.resolution


class QualityProfile(models.Model):
    """Quality profile that defines upgrade behavior and allowed qualities"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    allow_upgrades = models.BooleanField(default=True, help_text="Allow upgrading to better quality")
    upgrade_until_quality = models.ForeignKey(
        QualityDefinition,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='upgrade_until_profiles',
        help_text="Stop upgrading once this quality is reached"
    )
    minimum_custom_format_score = models.IntegerField(
        default=0,
        help_text="Minimum score required for downloads"
    )
    upgrade_until_custom_format_score = models.IntegerField(
        default=10000,
        help_text="Stop upgrading once this score is reached"
    )
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Ensure only one default profile
        if self.is_default:
            QualityProfile.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class QualityProfileItem(models.Model):
    """Links quality definitions to profiles with specific settings"""

    profile = models.ForeignKey(QualityProfile, on_delete=models.CASCADE, related_name='quality_items')
    quality = models.ForeignKey(QualityDefinition, on_delete=models.CASCADE)
    allowed = models.BooleanField(default=True, help_text="Is this quality allowed for download")
    order = models.IntegerField(default=0, help_text="Order within the profile (higher = preferred)")

    class Meta:
        unique_together = ['profile', 'quality']
        ordering = ['-order', 'quality__priority']

    def __str__(self):
        return f"{self.profile.name} - {self.quality.name}"


class CustomFormat(models.Model):
    """Custom format definitions for advanced quality scoring"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    include_patterns = models.JSONField(
        default=list,
        help_text="Patterns that must be present (regex supported)"
    )
    exclude_patterns = models.JSONField(
        default=list,
        help_text="Patterns that must NOT be present (regex supported)"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class CustomFormatProfileScore(models.Model):
    """Scoring for custom formats within a quality profile"""

    profile = models.ForeignKey(QualityProfile, on_delete=models.CASCADE, related_name='custom_format_scores')
    custom_format = models.ForeignKey(CustomFormat, on_delete=models.CASCADE)
    score = models.IntegerField(
        default=0,
        validators=[MinValueValidator(-10000), MaxValueValidator(10000)],
        help_text="Score for this custom format (-10000 to 10000)"
    )

    class Meta:
        unique_together = ['profile', 'custom_format']
        ordering = ['-score', 'custom_format__name']

    def __str__(self):
        return f"{self.profile.name} - {self.custom_format.name}: {self.score}"


class DownloadStatus(models.Model):
    """Model to track download status for episodes"""

    STATUS_CHOICES = [
        ('searching', 'Searching'),
        ('pending', 'Pending'),
        ('downloading', 'Downloading'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('monitoring', 'Monitoring'),
    ]

    SOURCE_CHOICES = [
        ('manual', 'Manual Search'),
        ('auto', 'Automatic Search'),
    ]

    # Link to the episode being downloaded
    episode = models.OneToOneField('Episode', on_delete=models.CASCADE, related_name='download_status')

    # Download details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='searching')
    source = models.CharField(max_length=10, choices=SOURCE_CHOICES, default='auto', help_text="How this download was initiated")
    torrent_title = models.CharField(max_length=500, blank=True)
    torrent_hash = models.CharField(max_length=40, blank=True)  # SHA1 hash
    magnet_url = models.TextField(blank=True)
    download_url = models.TextField(blank=True)

    # Real Debrid specific fields
    real_debrid_id = models.CharField(max_length=50, blank=True)
    real_debrid_links = models.JSONField(default=list, blank=True)  # Store download links

    # Quality and indexer info
    quality = models.CharField(max_length=20, blank=True)
    indexer = models.CharField(max_length=100, blank=True)
    size_bytes = models.BigIntegerField(null=True, blank=True)
    seeders = models.IntegerField(null=True, blank=True)

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_checked_at = models.DateTimeField(null=True, blank=True)

    # Error tracking
    error_message = models.TextField(blank=True)
    retry_count = models.IntegerField(default=0)

    # Cleanup tracking - ID of old download to clean up after this one completes
    cleanup_after_completion = models.CharField(max_length=50, blank=True, help_text="Real Debrid ID of old download to clean up after this download completes")

    class Meta:
        ordering = ['-started_at']

    @property
    def size_formatted(self):
        """Return formatted file size"""
        if not self.size_bytes:
            return "Unknown"

        size = self.size_bytes
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def __str__(self):
        return f"{self.episode} - {self.status}"

    @property
    def size_formatted(self):
        """Format size in bytes to human readable format"""
        if not self.size_bytes:
            return 'Unknown'

        size = self.size_bytes
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def get_quality_object(self):
        """Get MediaQuality object based on download quality info"""
        from media_requests.models import MediaQuality

        if not self.quality:
            return None

        # Try to determine source from torrent title
        source = 'Unknown'
        if self.torrent_title:
            title_upper = self.torrent_title.upper()
            if 'REMUX' in title_upper:
                source = 'REMUX'
            elif 'BLURAY' in title_upper or 'BLU-RAY' in title_upper:
                source = 'BluRay'
            elif 'WEB-DL' in title_upper or 'WEBDL' in title_upper:
                source = 'WEB-DL'
            elif 'WEBRIP' in title_upper or 'WEB-RIP' in title_upper:
                source = 'WEBRip'
            elif 'WEB' in title_upper:
                source = 'WEB'
            elif 'HDTV' in title_upper:
                source = 'HDTV'
            elif 'DVD' in title_upper:
                source = 'DVD'

        # Try to determine codec from torrent title
        codec = 'Unknown'
        if self.torrent_title:
            title_upper = self.torrent_title.upper()
            if 'AV1' in title_upper:
                codec = 'AV1'
            elif 'H265' in title_upper or 'HEVC' in title_upper or 'X265' in title_upper or 'H 265' in title_upper:
                codec = 'h265'
            elif 'H264' in title_upper or 'AVC' in title_upper or 'X264' in title_upper or 'H 264' in title_upper:
                codec = 'h264'

        try:
            quality_obj, _ = MediaQuality.objects.get_or_create(
                resolution=self.quality,
                source=source,
                codec=codec,
                defaults={}
            )
            return quality_obj
        except Exception:
            return None

    def cleanup_real_debrid(self):
        """Clean up this download from Real Debrid"""
        if self.real_debrid_id:
            try:
                from media_requests.real_debrid_service import RealDebridService
                rd_service = RealDebridService()
                success = rd_service.delete_torrent(self.real_debrid_id)
                if success:
                    self.real_debrid_id = ''
                    self.real_debrid_links = []
                    self.save(update_fields=['real_debrid_id', 'real_debrid_links'])
                return success
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Failed to cleanup Real Debrid torrent {self.real_debrid_id}: {e}")
                return False
        return True

    def cleanup_via_plex(self):
        """Clean up the episode file via Plex API if no Real Debrid ID is available"""
        if self.real_debrid_id:
            # If we have a Real Debrid ID, use that method instead
            return self.cleanup_real_debrid()

        try:
            from .services import PlexClient
            plex_service = PlexClient()

            # Find the episode in Plex
            plex_episode = plex_service.find_episode(
                show_title=self.episode.season.tv_show.title,
                season_number=self.episode.season.season_number,
                episode_number=self.episode.episode_number
            )

            if plex_episode:
                # Delete the episode from Plex (this will remove the file)
                plex_episode.delete()
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Successfully deleted episode via Plex: {self.episode}")
                return True
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Episode not found in Plex: {self.episode}")
                return False

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error cleaning up episode via Plex for {self.episode}: {e}")
            return False
